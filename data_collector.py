import os
import json
import random
import time
import logging
from typing import Optional, Dict, List, Any
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
from fuzzywuzzy import fuzz
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataCollector:
    def __init__(self):
        self.proxies_url = os.environ.get('PROXIES')
        self.db_connection_string = os.environ.get('DB')
        self.endpoint_url = os.environ.get('URL')

        if not self.proxies_url:
            raise ValueError("PROXIES environment variable not set")
        if not self.db_connection_string:
            raise ValueError("DB environment variable not set")
        if not self.endpoint_url:
            raise ValueError("URL environment variable not set")

        self.session = requests.Session()
        
    def get_db_connection(self):
        """Create and return a database connection"""
        try:
            conn = psycopg2.connect(self.db_connection_string)
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
            
    def get_random_player_with_null_manaf_id(self) -> Optional[Dict[str, Any]]:
        """Get a random player from the database where manaf_id is null"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute("""
                        SELECT name, firstName, lastName, rating, area_name, teamId, manaf_id
                        FROM public.top_pls 
                        WHERE manaf_id IS NULL 
                        ORDER BY RANDOM() 
                        LIMIT 1
                    """)
                    result = cursor.fetchone()
                    return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error fetching random player: {e}")
            return None
            
    def update_manaf_id(self, team_id: int, manaf_id: int) -> bool:
        """Update the manaf_id for a specific player"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        UPDATE public.top_pls 
                        SET manaf_id = %s 
                        WHERE teamId = %s AND manaf_id IS NULL
                    """, (manaf_id, team_id))
                    conn.commit()
                    return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating manaf_id: {e}")
            return False
            
    def make_api_request(self, first_name: str, last_name: str) -> Optional[List[Dict[str, Any]]]:
        """Make API request to the endpoint with proxy rotation"""
        full_name = f"{first_name} {last_name}"

        # Prepare request data based on the provided format
        payload = {
            "competitionLists": [],
            "competitionsIds": [],
            "keyword": full_name,
            "isForLoan": False,
            "isForSale": False,
            "isForFree": False,
            "isInReleaseList": False,
            "recommended": False,
            "minMarketValue": 0,
            "maxMarketValue": 75000000,
            "minAge": 16,
            "maxAge": 40,
            "contractExpiryMin": 0,
            "contractExpiryMax": 120,
            "positions": [],
            "isToBuy": True,
            "isToLoan": True,
            "minTransferFee": 0,
            "maxTransferFee": 500000000,
            "minLoanFee": 0,
            "maxLoanFee": 5000000,
            "gsnMin": 0,
            "gsnMax": 100,
            "mpMin": 0,
            "mpMax": 100,
            "faPointsPassResult": False,
            "faPointsAutoPassResult": False,
            "faPointsExemptionsPanelResult": False,
            "faPointsUkNationalsOrSettled": False,
            "isForFaPlayerPoints": True
        }

        # Random headers
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]

        headers = {
            'Content-Type': 'application/json',
            'User-Agent': random.choice(user_agents),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }
        
        # Use rotating proxy
        proxies = {
            'http': self.proxies_url,
            'https': self.proxies_url
        }
        
        try:
            logger.info(f"Making API request for: {full_name}")
            response = self.session.post(
                self.endpoint_url,
                json=payload,
                headers=headers,
                proxies=proxies,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"API request failed with status {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error making API request: {e}")
            return None
            
    def find_best_match(self, api_results: List[Dict[str, Any]], team_name: str) -> Optional[int]:
        """Find the best matching player based on team name similarity"""
        if not api_results:
            return None
            
        best_match_id = None
        best_score = 0
        
        for result in api_results:
            current_squad_name = result.get('currentSquadName')
            if current_squad_name:
                # Calculate similarity score
                score = fuzz.ratio(team_name.lower(), current_squad_name.lower()) / 100.0
                logger.info(f"Comparing '{team_name}' with '{current_squad_name}': score = {score:.3f}")
                
                if score > best_score and score > 0.8:
                    best_score = score
                    best_match_id = result.get('id')
                    
        if best_match_id:
            logger.info(f"Best match found with score {best_score:.3f}, ID: {best_match_id}")
        else:
            logger.info("No match found with score > 0.8")
            
        return best_match_id
        
    def process_single_player(self) -> bool:
        """Process a single random player with null manaf_id"""
        # Get random player
        player = self.get_random_player_with_null_manaf_id()
        if not player:
            logger.info("No players with null manaf_id found")
            return False
            
        logger.info(f"Processing player: {player['firstName']} {player['lastName']} (Team: {player['name']})")
        
        # Make API request
        api_results = self.make_api_request(player['firstName'], player['lastName'])
        if not api_results:
            logger.warning("No API results received")
            return False
            
        logger.info(f"Received {len(api_results)} results from API")
        
        # Always use the first result's ID as primary choice
        first_result_id = api_results[0].get('id') if api_results else None
        if first_result_id:
            logger.info(f"Using first result ID: {first_result_id}")
            if self.update_manaf_id(player['teamId'], first_result_id):
                logger.info(f"Successfully updated manaf_id to {first_result_id}")
                return True
        
        # If first result update failed, try to find best match
        best_match_id = self.find_best_match(api_results, player['name'])
        if best_match_id and best_match_id != first_result_id:
            logger.info(f"Trying best match ID: {best_match_id}")
            if self.update_manaf_id(player['teamId'], best_match_id):
                logger.info(f"Successfully updated manaf_id to {best_match_id}")
                return True
                
        logger.warning("Failed to update player")
        return False
        
    def run_single_collection(self):
        """Run a single data collection cycle"""
        try:
            success = self.process_single_player()
            if success:
                logger.info("Single collection completed successfully")
            else:
                logger.info("No players to process or processing failed")
            return success
        except Exception as e:
            logger.error(f"Error during single collection: {e}")
            return False

def main():
    """Main function to run the data collector"""
    try:
        collector = DataCollector()
        logger.info("Data collector initialized successfully")

        # Run single collection automatically
        success = collector.run_single_collection()

        if success:
            logger.info("Data collection completed successfully")
        else:
            logger.warning("Data collection failed or no data to process")

    except Exception as e:
        logger.error(f"Failed to initialize or run data collector: {e}")
        raise

if __name__ == "__main__":
    main()
