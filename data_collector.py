import os
import json
import random
import time
import logging
from typing import Optional, Dict, List, Any
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
from fuzzywuzzy import fuzz

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataCollector:
    def __init__(self):
        self.proxies_url = os.environ.get('PROXIES')
        self.db_connection_string = os.environ.get('DB')
        self.endpoint_url = None  # Will be set when provided
        
        if not self.proxies_url:
            raise ValueError("PROXIES environment variable not set")
        if not self.db_connection_string:
            raise ValueError("DB environment variable not set")
            
        self.session = requests.Session()
        
    def get_db_connection(self):
        """Create and return a database connection"""
        try:
            conn = psycopg2.connect(self.db_connection_string)
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
            
    def get_random_player_with_null_manaf_id(self) -> Optional[Dict[str, Any]]:
        """Get a random player from the database where manaf_id is null"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute("""
                        SELECT name, firstName, lastName, rating, area_name, teamId, manaf_id
                        FROM public.top_pls 
                        WHERE manaf_id IS NULL 
                        ORDER BY RANDOM() 
                        LIMIT 1
                    """)
                    result = cursor.fetchone()
                    return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error fetching random player: {e}")
            return None
            
    def update_manaf_id(self, team_id: int, manaf_id: int) -> bool:
        """Update the manaf_id for a specific player"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        UPDATE public.top_pls 
                        SET manaf_id = %s 
                        WHERE teamId = %s AND manaf_id IS NULL
                    """, (manaf_id, team_id))
                    conn.commit()
                    return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating manaf_id: {e}")
            return False
            
    def make_api_request(self, first_name: str, last_name: str) -> Optional[List[Dict[str, Any]]]:
        """Make API request to the endpoint with proxy rotation"""
        if not self.endpoint_url:
            raise ValueError("Endpoint URL not set. Please call set_endpoint_url() first.")
            
        full_name = f"{first_name} {last_name}"
        
        # Prepare request data - adjust this based on actual API requirements
        payload = {
            "name": full_name,
            "firstName": first_name,
            "lastName": last_name
        }
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        # Use rotating proxy
        proxies = {
            'http': self.proxies_url,
            'https': self.proxies_url
        }
        
        try:
            logger.info(f"Making API request for: {full_name}")
            response = self.session.post(
                self.endpoint_url,
                json=payload,
                headers=headers,
                proxies=proxies,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"API request failed with status {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error making API request: {e}")
            return None
            
    def find_best_match(self, api_results: List[Dict[str, Any]], team_name: str) -> Optional[int]:
        """Find the best matching player based on team name similarity"""
        if not api_results:
            return None
            
        best_match_id = None
        best_score = 0
        
        for result in api_results:
            current_squad_name = result.get('currentSquadName')
            if current_squad_name:
                # Calculate similarity score
                score = fuzz.ratio(team_name.lower(), current_squad_name.lower()) / 100.0
                logger.info(f"Comparing '{team_name}' with '{current_squad_name}': score = {score:.3f}")
                
                if score > best_score and score > 0.8:
                    best_score = score
                    best_match_id = result.get('id')
                    
        if best_match_id:
            logger.info(f"Best match found with score {best_score:.3f}, ID: {best_match_id}")
        else:
            logger.info("No match found with score > 0.8")
            
        return best_match_id
        
    def process_single_player(self) -> bool:
        """Process a single random player with null manaf_id"""
        # Get random player
        player = self.get_random_player_with_null_manaf_id()
        if not player:
            logger.info("No players with null manaf_id found")
            return False
            
        logger.info(f"Processing player: {player['firstName']} {player['lastName']} (Team: {player['name']})")
        
        # Make API request
        api_results = self.make_api_request(player['firstName'], player['lastName'])
        if not api_results:
            logger.warning("No API results received")
            return False
            
        logger.info(f"Received {len(api_results)} results from API")
        
        # Always use the first result's ID as primary choice
        first_result_id = api_results[0].get('id') if api_results else None
        if first_result_id:
            logger.info(f"Using first result ID: {first_result_id}")
            if self.update_manaf_id(player['teamId'], first_result_id):
                logger.info(f"Successfully updated manaf_id to {first_result_id}")
                return True
        
        # If first result update failed, try to find best match
        best_match_id = self.find_best_match(api_results, player['name'])
        if best_match_id and best_match_id != first_result_id:
            logger.info(f"Trying best match ID: {best_match_id}")
            if self.update_manaf_id(player['teamId'], best_match_id):
                logger.info(f"Successfully updated manaf_id to {best_match_id}")
                return True
                
        logger.warning("Failed to update player")
        return False
        
    def set_endpoint_url(self, url: str):
        """Set the API endpoint URL"""
        self.endpoint_url = url
        logger.info(f"Endpoint URL set to: {url}")
        
    def run_continuous(self, delay_seconds: int = 5, max_iterations: Optional[int] = None):
        """Run the collector continuously"""
        if not self.endpoint_url:
            raise ValueError("Endpoint URL not set. Please call set_endpoint_url() first.")
            
        iteration = 0
        while True:
            if max_iterations and iteration >= max_iterations:
                break
                
            try:
                success = self.process_single_player()
                if not success:
                    logger.info("No more players to process or processing failed")
                    break
                    
                iteration += 1
                logger.info(f"Completed iteration {iteration}")
                
                if delay_seconds > 0:
                    logger.info(f"Waiting {delay_seconds} seconds before next iteration...")
                    time.sleep(delay_seconds)
                    
            except KeyboardInterrupt:
                logger.info("Process interrupted by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                time.sleep(delay_seconds)
                
        logger.info("Data collection completed")

def main():
    """Main function to run the data collector"""
    collector = DataCollector()
    
    # You'll need to set the endpoint URL when you have it
    # collector.set_endpoint_url("https://your-api-endpoint.com/search")
    
    # Process a single player
    # collector.process_single_player()
    
    # Or run continuously
    # collector.run_continuous(delay_seconds=5, max_iterations=10)
    
    print("Data collector initialized. Use the following methods:")
    print("1. collector.set_endpoint_url('your-endpoint-url')")
    print("2. collector.process_single_player() - process one player")
    print("3. collector.run_continuous() - run continuously")

if __name__ == "__main__":
    main()
