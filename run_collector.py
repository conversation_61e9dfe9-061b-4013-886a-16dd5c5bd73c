#!/usr/bin/env python3
"""
Usage example for the data collector script.
Make sure to set your environment variables before running:
- DB: PostgreSQL connection string
- PROXIES: Rotating proxy URL
"""

import os
from data_collector import DataCollector

def main():
    # Check environment variables
    if not os.environ.get('DB'):
        print("Error: DB environment variable not set")
        return
        
    if not os.environ.get('PROXIES'):
        print("Error: PROXIES environment variable not set")
        return
    
    # Initialize collector
    try:
        collector = DataCollector()
        print("Data collector initialized successfully!")
        
        # Set your endpoint URL here when you have it
        endpoint_url = input("Enter the API endpoint URL: ").strip()
        if endpoint_url:
            collector.set_endpoint_url(endpoint_url)
            
            # Ask user what they want to do
            print("\nChoose an option:")
            print("1. Process a single random player")
            print("2. Run continuously (with delay)")
            print("3. Run a specific number of iterations")
            
            choice = input("Enter your choice (1-3): ").strip()
            
            if choice == "1":
                print("\nProcessing a single player...")
                success = collector.process_single_player()
                if success:
                    print("Player processed successfully!")
                else:
                    print("Failed to process player or no players available.")
                    
            elif choice == "2":
                delay = int(input("Enter delay between requests (seconds, default 5): ") or "5")
                print(f"\nRunning continuously with {delay}s delay...")
                print("Press Ctrl+C to stop")
                collector.run_continuous(delay_seconds=delay)
                
            elif choice == "3":
                max_iter = int(input("Enter number of iterations: "))
                delay = int(input("Enter delay between requests (seconds, default 5): ") or "5")
                print(f"\nRunning {max_iter} iterations with {delay}s delay...")
                collector.run_continuous(delay_seconds=delay, max_iterations=max_iter)
                
            else:
                print("Invalid choice")
        else:
            print("No endpoint URL provided. You can set it later using:")
            print("collector.set_endpoint_url('your-endpoint-url')")
            
    except Exception as e:
        print(f"Error initializing collector: {e}")

if __name__ == "__main__":
    main()
