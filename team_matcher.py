import os
import json
import random
import logging
from typing import Optional, Dict, List, Any
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
from fuzzywuzzy import fuzz
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TeamMatcher:
    def __init__(self):
        self.proxies_url = os.environ.get('PROXIES')
        self.db_connection_string = os.environ.get('DB')
        self.endpoint_url = os.environ.get('URL')
        
        if not self.proxies_url:
            raise ValueError("PROXIES environment variable not set")
        if not self.db_connection_string:
            raise ValueError("DB environment variable not set")
        if not self.endpoint_url:
            raise ValueError("URL environment variable not set")
            
        self.session = requests.Session()
        
    def get_db_connection(self):
        """Create and return a database connection"""
        try:
            conn = psycopg2.connect(self.db_connection_string)
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
            
    def get_random_firstname_with_null_manaf_id(self) -> Optional[str]:
        """Get a random firstName from players where manaf_id is null"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute("""
                        SELECT DISTINCT "firstName", random()
                        FROM public.top_pls 
                        WHERE manaf_id IS NULL 
                        AND "firstName" IS NOT NULL
                        AND "firstName" != ''
                        order by random()
                        LIMIT 1
                    """)
                    result = cursor.fetchone()
                    if result:
                        full_firstname = result['firstName']
                        # Split and get first part if multiple names
                        first_part = full_firstname.split()[0] if full_firstname else None
                        logger.info(f"Selected firstName: '{full_firstname}' -> using: '{first_part}'")
                        return first_part
                    return None
        except Exception as e:
            logger.error(f"Error fetching random firstName: {e}")
            return None
            
    def get_all_teams_with_null_manaf_id(self) -> List[Dict[str, Any]]:
        """Get all teams from the database where manaf_id is null"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute("""
                        SELECT name, "teamId"
                        FROM public.top_pls 
                        WHERE manaf_id IS NULL
                    """)
                    results = cursor.fetchall()
                    return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"Error fetching teams: {e}")
            return []
            
    def update_manaf_id(self, team_id: int, manaf_id: int) -> bool:
        """Update the manaf_id for a specific team"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        UPDATE public.top_pls 
                        SET manaf_id = %s 
                        WHERE "teamId" = %s AND manaf_id IS NULL
                    """, (manaf_id, team_id))
                    conn.commit()
                    return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating manaf_id: {e}")
            return False
            
    def make_api_request(self, first_name: str) -> Optional[List[Dict[str, Any]]]:
        """Make API request to the endpoint with proxy rotation"""
        # Prepare request data based on the provided format
        payload = {
            "competitionLists": [],
            "competitionsIds": [],
            "keyword": first_name,
            "isForLoan": False,
            "isForSale": False,
            "isForFree": False,
            "isInReleaseList": False,
            "recommended": False,
            "minMarketValue": 0,
            "maxMarketValue": 75000000,
            "minAge": 16,
            "maxAge": 40,
            "contractExpiryMin": 0,
            "contractExpiryMax": 120,
            "positions": [],
            "isToBuy": True,
            "isToLoan": True,
            "minTransferFee": 0,
            "maxTransferFee": 500000000,
            "minLoanFee": 0,
            "maxLoanFee": 5000000,
            "gsnMin": 0,
            "gsnMax": 100,
            "mpMin": 0,
            "mpMax": 100,
            "faPointsPassResult": False,
            "faPointsAutoPassResult": False,
            "faPointsExemptionsPanelResult": False,
            "faPointsUkNationalsOrSettled": False,
            "isForFaPlayerPoints": True
        }
        
        # Random headers
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': random.choice(user_agents),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }
        
        # Use rotating proxy
        proxies = {
            'http': self.proxies_url,
            'https': self.proxies_url
        }
        
        try:
            logger.info(f"Making API request for firstName: {first_name}")
            response = self.session.post(
                self.endpoint_url,
                json=payload,
                headers=headers,
                proxies=proxies,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"API request failed with status {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error making API request: {e}")
            return None
            
    def find_team_matches_for_all_results(self, api_results: List[Dict[str, Any]]) -> int:
        """Find team matches for all API results against all teams in database"""
        if not api_results:
            return 0
            
        # Get all teams with null manaf_id
        all_teams = self.get_all_teams_with_null_manaf_id()
        if not all_teams:
            logger.info("No teams with null manaf_id found")
            return 0
            
        updates_made = 0
        
        # Skip patterns for youth/reserve teams
        skip_patterns = ['U15', 'U16', 'U17', 'U18', 'U19', 'U21', 'U23', 'II', 'III', 'youth', 'reserve']

        # For each API result, check against all teams in our database
        for result in api_results:
            current_squad_name = result.get('currentSquadName')
            result_id = result.get('id')

            if not current_squad_name or not result_id:
                continue

            # Skip youth/reserve teams
            if any(pattern in current_squad_name for pattern in skip_patterns):
                logger.debug(f"Skipping youth/reserve team: {current_squad_name}")
                continue
                
            best_match_team = None
            best_score = 0
            
            # Compare this API result against all teams in our database
            for team in all_teams:
                team_name = team.get('name')
                if not team_name:
                    continue
                    
                score = fuzz.ratio(team_name.lower(), current_squad_name.lower()) / 100.0
                
                if score > best_score and score > 0.8:
                    best_score = score
                    best_match_team = team
                    
            # If we found a good match, update it
            if best_match_team and best_score > 0.8:
                team_id = best_match_team['teamId']
                logger.info(f"Found match: '{best_match_team['name']}' <-> '{current_squad_name}' (score: {best_score:.3f})")
                
                if self.update_manaf_id(team_id, result_id):
                    logger.info(f"✅ Updated team '{best_match_team['name']}' with manaf_id: {result_id}")
                    updates_made += 1
                    # Remove this team from the list to avoid duplicate updates
                    all_teams = [t for t in all_teams if t['teamId'] != team_id]
                    
        return updates_made
        
    def run_team_matching(self) -> bool:
        """Run team matching process"""
        # Get random firstName
        first_name = self.get_random_firstname_with_null_manaf_id()
        if not first_name:
            logger.info("No firstNames with null manaf_id found")
            return False
            
        logger.info(f"🔍 Starting team matching for firstName: '{first_name}'")
        
        # Make API request
        api_results = self.make_api_request(first_name)
        if not api_results:
            logger.warning("No API results received")
            return False
            
        logger.info(f"📊 Received {len(api_results)} results from API")
        
        # Find team matches for all results
        team_updates = self.find_team_matches_for_all_results(api_results)
        
        if team_updates > 0:
            logger.info(f"🎉 Successfully updated {team_updates} teams based on squad name matches")
            return True
        else:
            logger.info("ℹ️ No team matches found with score > 0.8")
            return False

def main():
    """Main function to run the team matcher"""
    try:
        matcher = TeamMatcher()
        logger.info("Team matcher initialized successfully")
        
        # Run team matching
        success = matcher.run_team_matching()
        
        if success:
            logger.info("✅ Team matching completed successfully")
        else:
            logger.warning("⚠️ Team matching completed but no updates made")
            
    except Exception as e:
        logger.error(f"❌ Failed to run team matcher: {e}")
        raise

if __name__ == "__main__":
    main()
