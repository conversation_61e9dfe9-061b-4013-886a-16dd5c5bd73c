#!/usr/bin/env python3
"""
Team matching script.
Gets a random firstName and matches API results to teams in database.
Requires environment variables: DB, PROXIES, URL
"""

import sys
from team_matcher import TeamMatcher

def main():
    """Run the automated team matcher"""
    try:
        # Initialize and run team matcher automatically
        matcher = TeamMatcher()
        success = matcher.run_team_matching()
        
        if success:
            print("✅ Team matching completed successfully")
            sys.exit(0)
        else:
            print("⚠️ No team matches found")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
